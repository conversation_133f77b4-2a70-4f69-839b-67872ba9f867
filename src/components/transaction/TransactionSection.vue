<template>
  <div class="space-y-4">
    <!-- Grouped by Date (for deposits) -->
    <div v-if="groupedByDate && groupedTransactions">
      <div v-for="(dayTransactions, date) in groupedTransactions" :key="date" class="space-y-3">
        <!-- Date Header -->
        <div class="flex items-center">
          <div class="flex-1 border-t border-gray-200"></div>
          <div class="px-4 py-2 bg-gray-100 rounded-full text-sm font-medium text-gray-600">
            {{ formatDate(date) }}
          </div>
          <div class="flex-1 border-t border-gray-200"></div>
        </div>

        <!-- Transactions for this date -->
        <div class="space-y-2">
          <TransactionItem
            v-for="transaction in dayTransactions"
            :key="transaction.id"
            :transaction="transaction"
            @copy-order="handleCopyOrder"
          />
        </div>
      </div>
    </div>

    <!-- Simple List (for other transaction types) -->
    <div v-else class="space-y-2">
      <TransactionItem
        v-for="transaction in transactions"
        :key="transaction.id"
        :transaction="transaction"
        @copy-order="handleCopyOrder"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { TransactionItem as TransactionItemType } from '@/types/api'
import TransactionItem from './TransactionItem.vue'
import {
  formatRelativeDate,
  getSmartTransactionDate,
  extractDateKeysFromGroupedTransactions
} from '@/utils/date'

interface Props {
  transactions: TransactionItemType[]
  groupedByDate?: boolean
  // 如果交易已经按日期分组（如存款记录），可以直接传入分组数据
  groupedTransactions?: Record<string, TransactionItemType[]>
}

const props = withDefaults(defineProps<Props>(), {
  groupedByDate: false,
  groupedTransactions: undefined
})

const emit = defineEmits<{
  'copy-order': [orderId: string]
}>()

const { t } = useI18n()

// Computed
const groupedTransactions = computed(() => {
  if (!props.groupedByDate) return null

  // 如果已经提供了分组数据，直接使用
  if (props.groupedTransactions) {
    // 确保日期键按时间倒序排列
    const sortedKeys = extractDateKeysFromGroupedTransactions(props.groupedTransactions)
    const sortedGrouped: Record<string, TransactionItemType[]> = {}

    sortedKeys.forEach((key) => {
      sortedGrouped[key] = props.groupedTransactions![key]
    })

    return sortedGrouped
  }

  // 否则从交易列表中动态分组
  const grouped: Record<string, TransactionItemType[]> = {}

  props.transactions.forEach((transaction) => {
    // 使用智能日期提取
    const date = getSmartTransactionDate(transaction)
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(transaction)
  })

  // 按日期倒序排列
  const sortedKeys = extractDateKeysFromGroupedTransactions(grouped)
  const sortedGrouped: Record<string, TransactionItemType[]> = {}

  sortedKeys.forEach((key) => {
    sortedGrouped[key] = grouped[key]
  })

  return sortedGrouped
})

// Methods
const formatDate = (dateString: string): string => {
  return formatRelativeDate(dateString, t)
}

const handleCopyOrder = (orderId: string) => {
  emit('copy-order', orderId)
}
</script>
