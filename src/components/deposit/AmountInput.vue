<template>
  <div class="space-y-3">
    <!-- Amount Input Field -->
    <div class="relative">
      <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
        {{ currency }}
      </div>
      <input
        :value="displayValue"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        type="text"
        inputmode="numeric"
        :placeholder="`${currency}0`"
        :class="[
          'w-full pl-8 pr-4 py-4 text-xl font-semibold border rounded-xl transition-colors',
          hasError
            ? 'border-red-300 bg-red-50 text-red-900 placeholder-red-400'
            : isFocused
            ? 'border-blue-500 bg-blue-50 text-blue-900'
            : 'border-gray-200 bg-white text-gray-900'
        ]"
      />
    </div>
    
    <!-- Error Message -->
    <div v-if="hasError" class="text-sm text-red-600">
      {{ errorMessage }}
    </div>
    
    <!-- Amount Limits Info -->
    <div v-if="showLimits" class="text-xs text-gray-500 text-center">
      {{ $t('deposit.minMaxAmount', { min: formatCurrency(min), max: formatCurrency(max) }) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { formatCurrency, parseCurrency } from '@/utils/currency'

interface Props {
  modelValue: number
  min?: number
  max?: number
  currency?: string
  showLimits?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  min: 0,
  max: 1000000,
  currency: '₹',
  showLimits: true
})

const emit = defineEmits<{
  'update:modelValue': [value: number]
}>()

const { t } = useI18n()

// State
const isFocused = ref(false)
const inputValue = ref('')

// Computed
const displayValue = computed(() => {
  if (isFocused.value) {
    return inputValue.value
  }
  return props.modelValue > 0 ? props.modelValue.toString() : ''
})

const hasError = computed(() => {
  if (props.modelValue === 0) return false
  return props.modelValue < props.min || props.modelValue > props.max
})

const errorMessage = computed(() => {
  if (props.modelValue < props.min) {
    return t('deposit.minMaxAmount', { 
      min: formatCurrency(props.min), 
      max: formatCurrency(props.max) 
    })
  }
  if (props.modelValue > props.max) {
    return t('deposit.minMaxAmount', { 
      min: formatCurrency(props.min), 
      max: formatCurrency(props.max) 
    })
  }
  return ''
})

// Methods
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  let value = target.value
  
  // Remove non-numeric characters except decimal point
  value = value.replace(/[^\d.]/g, '')
  
  // Ensure only one decimal point
  const parts = value.split('.')
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('')
  }
  
  // Limit decimal places to 2
  if (parts[1] && parts[1].length > 2) {
    value = parts[0] + '.' + parts[1].substring(0, 2)
  }
  
  inputValue.value = value
  
  // Emit numeric value
  const numericValue = parseCurrency(value)
  emit('update:modelValue', numericValue)
}

const handleFocus = () => {
  isFocused.value = true
  inputValue.value = props.modelValue > 0 ? props.modelValue.toString() : ''
}

const handleBlur = () => {
  isFocused.value = false
  inputValue.value = ''
}

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (!isFocused.value && newValue !== parseCurrency(inputValue.value)) {
    inputValue.value = newValue > 0 ? newValue.toString() : ''
  }
})
</script>
