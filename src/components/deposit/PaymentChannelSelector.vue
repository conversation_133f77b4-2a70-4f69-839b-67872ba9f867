<template>
  <div class="space-y-3">
    <div
      v-for="channel in channels"
      :key="channel.id"
      @click="selectChannel(channel)"
      :class="[
        'p-4 border rounded-xl cursor-pointer transition-all duration-200',
        isSelected(channel)
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
      ]"
    >
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <!-- Channel Title -->
          <h4 class="font-medium text-gray-900 mb-1">{{ channel.title }}</h4>

          <!-- Channel Limits -->
          <p class="text-sm text-gray-600 mb-2">
            Limits: {{ formatCurrency(channel.mincoin) }} - {{ formatCurrency(channel.maxcoin) }}
          </p>

          <!-- Fee Information -->
          <div class="flex items-center justify-between">
            <div class="text-sm">
              <span v-if="channel.rate > 0" class="text-orange-600">
                {{ t('deposit.fee') }}: {{ (channel.rate * 100).toFixed(2) }}%
              </span>
              <span v-else-if="channel.discoin > 0" class="text-green-600">
                {{ t('deposit.bonus') }}: {{ formatDiscount(channel.disrate, channel.discoin) }}
              </span>
              <span v-else class="text-gray-500">{{ t('deposit.noAdditionalFees') }}</span>
            </div>

            <!-- Total Amount Preview -->
            <div v-if="selectedAmount > 0 && isSelected(channel)" class="text-right">
              <div class="text-sm text-gray-600">{{ t('common.total') }}:</div>
              <div class="font-semibold text-gray-900">
                {{ formatCurrency(calculateTotal(channel)) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Selection Indicator -->
        <div class="ml-4">
          <div
            v-if="isSelected(channel)"
            class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
          >
            <Check class="w-4 h-4 text-white" />
          </div>
          <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full" />
        </div>
      </div>

      <!-- Fee Breakdown (for selected channel) -->
      <div
        v-if="
          isSelected(channel) && selectedAmount > 0 && (channel.rate > 0 || channel.discoin > 0)
        "
        class="mt-3 pt-3 border-t border-blue-200"
      >
        <div class="text-sm space-y-1">
          <div class="flex justify-between">
            <span class="text-gray-600">Amount:</span>
            <span>{{ formatCurrency(selectedAmount) }}</span>
          </div>
          <div v-if="channel.rate > 0" class="flex justify-between">
            <span class="text-gray-600">Fee ({{ (channel.rate * 100).toFixed(2) }}%):</span>
            <span class="text-orange-600"
              >+{{ formatCurrency(selectedAmount * channel.rate) }}</span
            >
          </div>
          <div v-if="channel.discoin > 0" class="flex justify-between">
            <span class="text-gray-600">{{ t('deposit.bonus') }}:</span>
            <span class="text-green-600">+{{ formatCurrency(channel.discoin) }}</span>
          </div>
          <div class="flex justify-between font-semibold border-t pt-1">
            <span>{{ t('common.total') }}:</span>
            <span>{{ formatCurrency(calculateTotal(channel)) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="channels.length === 0" class="text-center py-8">
      <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
        <CreditCard class="w-6 h-6 text-gray-400" />
      </div>
      <p class="text-gray-600">No payment channels available</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { CreditCard, Check } from 'lucide-vue-next'
import { formatCurrency, formatDiscount } from '@/utils/currency'
import { calculatePaymentTotal } from '@/utils/calculation'
import type { PaymentPage } from '@/types/api'

interface Props {
  modelValue: PaymentPage | null
  channels: PaymentPage[]
  selectedAmount: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: PaymentPage | null]
}>()

const { t } = useI18n()

// Computed
const selectedChannel = computed(() => props.modelValue)

// Methods
const selectChannel = (channel: PaymentPage) => {
  if (isSelected(channel)) {
    emit('update:modelValue', null)
  } else {
    emit('update:modelValue', channel)
  }
}

const isSelected = (channel: PaymentPage) => {
  return selectedChannel.value?.id === channel.id
}

const calculateTotal = (channel: PaymentPage) => {
  return calculatePaymentTotal(props.selectedAmount, channel)
}
</script>
