<template>
  <div :class="['animate-pulse', containerClass]">
    <!-- Card Skeleton -->
    <div v-if="type === 'card'" class="bg-white rounded-xl p-4 space-y-3">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gray-200 rounded-lg" />
        <div class="flex-1 space-y-2">
          <div class="h-4 bg-gray-200 rounded w-3/4" />
          <div class="h-3 bg-gray-200 rounded w-1/2" />
        </div>
      </div>
      <div class="space-y-2">
        <div class="h-3 bg-gray-200 rounded" />
        <div class="h-3 bg-gray-200 rounded w-5/6" />
      </div>
    </div>

    <!-- List Item Skeleton -->
    <div v-else-if="type === 'list-item'" class="flex items-center space-x-3 p-4">
      <div class="w-10 h-10 bg-gray-200 rounded-full" />
      <div class="flex-1 space-y-2">
        <div class="h-4 bg-gray-200 rounded w-3/4" />
        <div class="h-3 bg-gray-200 rounded w-1/2" />
      </div>
      <div class="w-16 h-6 bg-gray-200 rounded" />
    </div>

    <!-- Transaction Item Skeleton -->
    <div v-else-if="type === 'transaction'" class="bg-white rounded-xl p-4 space-y-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gray-200 rounded-full" />
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 rounded w-24" />
            <div class="h-3 bg-gray-200 rounded w-16" />
          </div>
        </div>
        <div class="text-right space-y-2">
          <div class="h-4 bg-gray-200 rounded w-20" />
          <div class="h-3 bg-gray-200 rounded w-16" />
        </div>
      </div>
      <div class="flex items-center justify-between pt-2 border-t border-gray-100">
        <div class="h-3 bg-gray-200 rounded w-32" />
        <div class="h-6 bg-gray-200 rounded-full w-16" />
      </div>
    </div>

    <!-- Balance Card Skeleton -->
    <div v-else-if="type === 'balance'" class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white">
      <div class="space-y-4">
        <div class="h-4 bg-white/20 rounded w-32" />
        <div class="h-8 bg-white/20 rounded w-48" />
        <div class="flex space-x-4">
          <div class="h-3 bg-white/20 rounded w-20" />
          <div class="h-3 bg-white/20 rounded w-24" />
        </div>
      </div>
    </div>

    <!-- Payment Method Skeleton -->
    <div v-else-if="type === 'payment-method'" class="border border-gray-200 rounded-xl p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-gray-200 rounded-lg" />
          <div class="space-y-2">
            <div class="h-4 bg-gray-200 rounded w-24" />
            <div class="h-3 bg-gray-200 rounded w-32" />
          </div>
        </div>
        <div class="text-right space-y-2">
          <div class="h-3 bg-gray-200 rounded w-16" />
          <div class="h-3 bg-gray-200 rounded w-12" />
        </div>
      </div>
    </div>

    <!-- Button Skeleton -->
    <div v-else-if="type === 'button'" :class="['bg-gray-200 rounded-xl', buttonSize]" />

    <!-- Text Skeleton -->
    <div v-else-if="type === 'text'" class="space-y-2">
      <div v-for="line in lines" :key="line" class="h-4 bg-gray-200 rounded" :style="{ width: `${Math.random() * 40 + 60}%` }" />
    </div>

    <!-- Avatar Skeleton -->
    <div v-else-if="type === 'avatar'" :class="['bg-gray-200 rounded-full', avatarSize]" />

    <!-- Custom Skeleton -->
    <div v-else-if="type === 'custom'" class="space-y-3">
      <slot />
    </div>

    <!-- Default Rectangle Skeleton -->
    <div v-else :class="['bg-gray-200 rounded', width, height]" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'card' | 'list-item' | 'transaction' | 'balance' | 'payment-method' | 'button' | 'text' | 'avatar' | 'custom' | 'rectangle'
  width?: string
  height?: string
  lines?: number
  size?: 'sm' | 'md' | 'lg' | 'xl'
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'rectangle',
  width: 'w-full',
  height: 'h-4',
  lines: 3,
  size: 'md',
  containerClass: ''
})

const buttonSize = computed(() => {
  const sizes = {
    sm: 'h-8 w-20',
    md: 'h-10 w-24',
    lg: 'h-12 w-32',
    xl: 'h-14 w-40'
  }
  return sizes[props.size]
})

const avatarSize = computed(() => {
  const sizes = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }
  return sizes[props.size]
})
</script>

<style scoped>
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 更平滑的脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 可选的闪光效果 */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
</style>
