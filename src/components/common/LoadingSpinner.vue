<template>
  <div class="flex items-center justify-center">
    <div class="relative">
      <!-- Spinner -->
      <div
        :class="[
          'border-4 border-gray-200 rounded-full animate-spin',
          sizeClasses,
          colorClasses
        ]"
      />
      
      <!-- Center dot (optional) -->
      <div
        v-if="showCenterDot"
        :class="[
          'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full',
          centerDotClasses
        ]"
      />
    </div>
    
    <!-- Loading text -->
    <span
      v-if="text"
      :class="[
        'ml-3 font-medium',
        textColorClasses
      ]"
    >
      {{ text }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white'
  text?: string
  showCenterDot?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: 'primary',
  showCenterDot: false
})

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  return sizes[props.size]
})

const colorClasses = computed(() => {
  const colors = {
    primary: 'border-t-blue-600',
    secondary: 'border-t-gray-600',
    white: 'border-t-white'
  }
  return colors[props.color]
})

const centerDotClasses = computed(() => {
  const sizes = {
    sm: 'w-1 h-1',
    md: 'w-1.5 h-1.5',
    lg: 'w-2 h-2',
    xl: 'w-3 h-3'
  }
  
  const colors = {
    primary: 'bg-blue-600',
    secondary: 'bg-gray-600',
    white: 'bg-white'
  }
  
  return `${sizes[props.size]} ${colors[props.color]}`
})

const textColorClasses = computed(() => {
  const colors = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  }
  return colors[props.color]
})
</script>
