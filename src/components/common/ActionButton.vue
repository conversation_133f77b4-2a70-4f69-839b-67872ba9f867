<template>
  <button :class="buttonClasses" :disabled="disabled || loading" @click="handleClick">
    <!-- Loading Spinner -->
    <div
      v-if="loading"
      class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"
    />

    <!-- Icon -->
    <component v-if="icon && !loading" :is="iconComponent" class="w-4 h-4 mr-2" />

    <!-- Text -->
    <span class="font-medium">{{ text }}</span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Plus, ArrowRight, Check, X, Download, Upload, CreditCard, User } from 'lucide-vue-next'

interface Props {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  icon?: string
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  loading: false,
  disabled: false
})

const emit = defineEmits<{
  click: []
}>()

const iconComponents = {
  plus: Plus,
  'arrow-right': ArrowRight,
  check: Check,
  x: X,
  download: Download,
  upload: Upload,
  'credit-card': CreditCard,
  user: User
}

const iconComponent = computed(() => {
  return props.icon ? iconComponents[props.icon as keyof typeof iconComponents] : null
})

const buttonClasses = computed(() => {
  const baseClasses =
    'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  }

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300',
    secondary:
      'bg-white text-blue-600 border border-blue-600 hover:bg-blue-50 focus:ring-blue-500 disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-300',
    outline:
      'bg-transparent text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500 disabled:bg-gray-100 disabled:text-gray-400',
    ghost:
      'bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 disabled:text-gray-400'
  }

  const disabledClasses =
    props.disabled || props.loading ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'

  return [
    baseClasses,
    sizeClasses[props.size],
    variantClasses[props.variant],
    disabledClasses
  ].join(' ')
})

const handleClick = async () => {
  if (!props.disabled && !props.loading) {
    // 添加触觉反馈
    const { useHaptics } = await import('@/composables/useHaptics')
    const { light } = useHaptics()
    light()

    emit('click')
  }
}
</script>
