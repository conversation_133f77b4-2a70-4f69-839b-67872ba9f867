<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="visible"
        class="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
        @click="handleBackdropClick"
      >
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="translate-y-full"
          enter-to-class="translate-y-0"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="translate-y-0"
          leave-to-class="translate-y-full"
        >
          <div
            v-if="visible"
            :class="[
              'fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-xl',
              'max-h-[90vh] overflow-hidden flex flex-col',
              sizeClasses
            ]"
            @click.stop
          >
            <!-- Handle -->
            <div class="flex justify-center py-3">
              <div class="w-10 h-1 bg-gray-300 rounded-full" />
            </div>

            <!-- Header -->
            <div v-if="title || $slots.header" class="px-6 pb-4">
              <slot name="header">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ title }}
                  </h3>
                  <button
                    v-if="closable"
                    @click="hide"
                    class="p-2 -mr-2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X class="w-5 h-5" />
                  </button>
                </div>
                <p v-if="subtitle" class="text-sm text-gray-600 mt-1">
                  {{ subtitle }}
                </p>
              </slot>
            </div>

            <!-- Content -->
            <div class="flex-1 overflow-y-auto px-6">
              <slot />
            </div>

            <!-- Footer -->
            <div v-if="$slots.footer" class="px-6 py-4 border-t border-gray-100 bg-gray-50/50">
              <slot name="footer" />
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { X } from 'lucide-vue-next'

interface Props {
  title?: string
  subtitle?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  closable?: boolean
  closeOnBackdrop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  closeOnBackdrop: true
})

const emit = defineEmits<{
  close: []
  open: []
}>()

const visible = ref(false)

const sizeClasses = computed(() => {
  const classes = {
    sm: 'max-h-[40vh]',
    md: 'max-h-[60vh]',
    lg: 'max-h-[80vh]',
    xl: 'max-h-[90vh]'
  }
  return classes[props.size]
})

const show = () => {
  visible.value = true
  emit('open')
}

const hide = () => {
  visible.value = false
  emit('close')
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    hide()
  }
}

defineExpose({
  show,
  hide
})
</script>
