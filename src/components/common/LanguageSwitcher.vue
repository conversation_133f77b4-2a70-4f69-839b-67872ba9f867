<template>
  <div class="relative">
    <!-- Language Button -->
    <button
      @click="toggleDropdown"
      :class="[
        'flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors',
        props.variant === 'header'
          ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
          : 'bg-white border border-gray-200 text-gray-700 hover:bg-gray-50'
      ]"
    >
      <Globe class="w-4 h-4" />
      <span class="text-sm font-medium">{{ currentLanguage.name }}</span>
      <ChevronDown :class="['w-4 h-4 transition-transform', isOpen ? 'rotate-180' : '']" />
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="isOpen"
      class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
    >
      <button
        v-for="language in languages"
        :key="language.code"
        @click="selectLanguage(language)"
        :class="[
          'w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors',
          currentLocale === language.code ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
        ]"
      >
        <span class="text-lg">{{ language.flag }}</span>
        <div>
          <div class="font-medium">{{ language.name }}</div>
          <div class="text-xs text-gray-500">{{ language.nativeName }}</div>
        </div>
        <CheckCircle v-if="currentLocale === language.code" class="w-4 h-4 text-blue-600 ml-auto" />
      </button>
    </div>

    <!-- Backdrop -->
    <div v-if="isOpen" @click="closeDropdown" class="fixed inset-0 z-40" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Globe, ChevronDown, CheckCircle } from 'lucide-vue-next'

interface Language {
  code: string
  name: string
  nativeName: string
  flag: string
}

interface Props {
  variant?: 'header' | 'standalone'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'standalone'
})

const { locale } = useI18n()

// State
const isOpen = ref(false)

// Available languages
const languages: Language[] = [
  {
    code: 'zh-CN',
    name: 'Chinese',
    nativeName: '中文 (简体)',
    flag: '🇨🇳'
  },
  {
    code: 'en-US',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'hi-IN',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳'
  },
  {
    code: 'pt-BR',
    name: 'Portuguese',
    nativeName: 'Português (Brasil)',
    flag: '🇧🇷'
  }
]

// Computed
const currentLocale = computed(() => locale.value)

const currentLanguage = computed(() => {
  return languages.find((lang) => lang.code === currentLocale.value) || languages[0]
})

// Methods
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const selectLanguage = (language: Language) => {
  locale.value = language.code

  // Save to localStorage
  localStorage.setItem('preferred-language', language.code)

  closeDropdown()

  // Emit event for parent components
  document.dispatchEvent(
    new CustomEvent('language-changed', {
      detail: { language: language.code }
    })
  )
}

// Handle click outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    closeDropdown()
  }
}

// Load saved language preference
const loadSavedLanguage = () => {
  const savedLanguage = localStorage.getItem('preferred-language')
  if (savedLanguage && languages.some((lang) => lang.code === savedLanguage)) {
    locale.value = savedLanguage
  }
}

// Lifecycle
onMounted(() => {
  loadSavedLanguage()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
