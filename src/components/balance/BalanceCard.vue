<template>
  <div
    :class="['rounded-xl p-6 shadow-sm transition-all duration-200 hover:shadow-md', cardClasses]"
  >
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <!-- Title -->
        <h3
          :class="[
            'text-sm font-medium uppercase tracking-wide',
            isPrimary ? 'text-white/90' : 'text-gray-600'
          ]"
        >
          {{ title }}
        </h3>

        <!-- Amount -->
        <div class="mt-2">
          <span :class="['text-2xl font-bold', isPrimary ? 'text-white' : 'text-gray-900']">
            {{ formattedAmount }}
          </span>
        </div>

        <!-- Subtitle Slot -->
        <div v-if="$slots.subtitle" class="mt-2">
          <slot name="subtitle" />
        </div>
      </div>

      <!-- Action Button -->
      <div v-if="$slots.action" class="ml-4">
        <slot name="action" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { formatCurrency } from '@/utils/currency'

interface Props {
  title: string
  amount: number
  isPrimary?: boolean
  currency?: string
}

const props = withDefaults(defineProps<Props>(), {
  isPrimary: false,
  currency: '₹'
})

const cardClasses = computed(() => {
  return props.isPrimary
    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
    : 'bg-white border border-gray-200'
})

const formattedAmount = computed(() => {
  return formatCurrency(props.amount, props.currency)
})
</script>
