export default {
  common: {
    balance: 'बैलेंस',
    deposit: 'जमा',
    withdraw: 'निकासी',
    transactions: 'लेन-देन',
    amount: 'राशि',
    submit: 'जमा करें',
    cancel: 'रद्द करें',
    loading: 'लोड हो रहा है...',
    error: 'त्रुटि',
    success: 'सफलता',
    confirm: 'पुष्टि करें',
    back: 'वापस',
    next: 'अगला',
    save: 'सेव करें',
    delete: 'हटाएं',
    edit: 'संपादित करें',
    add: 'जोड़ें',
    copy: 'कॉपी करें',
    contact: 'संपर्क करें',
    today: 'आज',
    yesterday: 'कल',
    total: 'कुल',
    retry: 'पुनः प्रयास करें'
  },
  home: {
    title: 'मेरा बैलेंस',
    totalBalance: 'कुल बैलेंस',
    amountAdded: 'जोड़ी गई राशि (अप्रयुक्त)',
    winnings: 'जीत',
    cashBonus: 'कैश बोनस',
    addCash: 'कैश जोड़ें',
    withdrawInstantly: 'तुरंत निकालें',
    verifyNow: 'अभी सत्यापित करें',
    myTransactions: 'मेरे लेन-देन',
    managePayments: 'भुगतान प्रबंधित करें',
    verifyAccount: 'निकासी के लिए पात्र होने के लिए अपने खाते को सत्यापित करें',
    cashBonusRule: 'प्रति मैच अधिकतम उपयोग योग्य कैश बोनस = प्रवेश शुल्क का 10%',
    knowMore: 'और जानें'
  },
  deposit: {
    title: 'जमा',
    currentBalance: 'वर्तमान बैलेंस',
    paymentMethod: 'भुगतान विधि',
    paymentChannel: 'भुगतान चैनल',
    depositNow: 'अभी जमा करें',
    minMaxAmount: 'जमा न्यूनतम: ₹{min} और अधिकतम: ₹{max} प्रत्येक बार अनुमतित',
    tips: 'सुझाव',
    cashBalance: 'कैश बैलेंस',
    cashBonus: 'कैश बोनस',
    depositUsuallyCredited: 'जमा आमतौर पर मिनटों में जमा हो जाता है',
    depositSubmitted: 'जमा {amount} जमा किया गया',
    depositSuccess: 'जमा सफल',
    depositFailed: 'जमा असफल, कृपया पुनः प्रयास करें',
    depositError: 'जमा असफल',
    fee: 'शुल्क',
    bonus: 'बोनस',
    noAdditionalFees: 'कोई अतिरिक्त शुल्क नहीं',
    optionCount: '{count, plural, =1 {विकल्प} other {विकल्प}}',
    noPaymentMethods: 'कोई भुगतान विधि उपलब्ध नहीं',
    includingFees: 'शुल्क सहित'
  },

  // फ़ाइल अपलोड
  fileUpload: {
    clickToUpload: 'अपलोड करने के लिए क्लिक करें या फ़ाइल खींचें',
    uploading: 'अपलोड हो रहा है...',
    fileTypeNotSupported: 'फ़ाइल प्रकार समर्थित नहीं है'
  },

  // लेज़ी इमेज
  lazyImage: {
    failedToLoad: 'लोड करने में असफल'
  },

  withdraw: {
    title: 'निकासी',
    withdrawableBalance: 'निकासी योग्य बैलेंस',
    addNewBankAccount: 'नया बैंक खाता जोड़ें',
    withdrawNow: 'अभी निकालें',
    minMaxAmount: 'निकासी न्यूनतम: ₹{min} और अधिकतम: ₹{max} प्रत्येक बार अनुमतित',
    addBankAccountDesc: 'निकासी के लिए एक नया बैंक खाता जोड़ें',
    withdrawUsuallyProcessed: 'निकासी आमतौर पर मिनटों में प्रोसेस हो जाती है'
  },

  transaction: {
    title: 'मेरे लेन-देन',
    deposits: 'जमा',
    withdrawals: 'निकासी',
    bet: 'बेट',
    bonus: 'बोनस',
    success: 'सफल',
    inProcess: 'प्रक्रिया में',
    failed: 'असफल',
    refund: 'रिफंड',
    win: 'जीत',
    loss: 'हार',
    transferIn: 'ट्रांसफर इन',
    transferOut: 'ट्रांसफर आउट',
    noTransactions: 'आपने अभी तक कोई लेन-देन नहीं किया है',
    needHelp: 'इस ऑर्डर के लिए मदद चाहिए? हमसे संपर्क करने के लिए टैप करें',
    allBettingRecords: 'आपके सभी बेटिंग रिकॉर्ड यहां प्रदर्शित हैं',
    allBonusRecords: 'आपके सभी बोनस रिकॉर्ड यहां प्रदर्शित हैं',
    noDepositTransactions: 'कोई जमा लेन-देन नहीं मिला',
    noWithdrawalTransactions: 'कोई निकासी लेन-देन नहीं मिला',
    orderCopied: 'ऑर्डर आईडी क्लिपबोर्ड पर कॉपी किया गया',
    copyFailed: 'कॉपी असफल, कृपया ऑर्डर आईडी मैन्युअल रूप से कॉपी करें'
  },
  payment: {
    title: 'भुगतान प्रबंधित करें',
    myBankAccounts: 'मेरे बैंक खाते',
    myWallets: 'मेरे वॉलेट UPI ID',
    verifyNewBankAccount: 'नया बैंक खाता सत्यापित करें',
    linkUpiWallets: 'त्वरित भुगतान के लिए अपने UPI वॉलेट को लिंक करें',
    noUpiWallets: 'कोई UPI वॉलेट लिंक नहीं है',
    linkUpiWalletsFaster: 'तेज़ भुगतान के लिए अपने UPI वॉलेट को लिंक करें',
    linked: 'लिंक किया गया',
    notLinked: 'लिंक नहीं किया गया',
    link: 'लिंक करें',
    unlink: 'अनलिंक करें',
    upiId: 'UPI ID',
    upiPlaceholder: 'yourname@upi',
    paytm: 'Paytm',
    phonepe: 'PhonePe',
    gpay: 'GPay',
    otherUpi: 'अन्य UPI ID',
    // वॉलेट विवरण
    paytmDescription: 'डिजिटल वॉलेट और UPI भुगतान',
    phonepeDescription: 'UPI भुगतान और पैसे का स्थानांतरण',
    gpayDescription: 'Google Pay UPI भुगतान',
    otherUpiDescription: 'कोई भी UPI ID',
    upiPaymentMethod: 'UPI भुगतान विधि',
    // बैंक खाता विवरण
    manageBankAccountsDesc: 'निकासी के लिए अपने बैंक खातों को प्रबंधित करें',
    // सहायता अनुभाग
    needHelpWithPayments: 'भुगतान में सहायता चाहिए?',
    contactSupportForPayments: 'भुगतान विधियों के लिए सहायता हेतु हमारी सहायता टीम से संपर्क करें',
    contactSupport: 'सहायता से संपर्क करें'
  },
  bankAccount: {
    title: 'नया बैंक खाता जोड़ें',
    accountNumber: 'खाता संख्या',
    retypeAccountNumber: 'खाता संख्या फिर से टाइप करें',
    ifscCode: 'IFSC कोड',
    name: 'नाम',
    verified: 'सत्यापित',
    pending: 'लंबित',
    bankProof: 'बैंक प्रमाण',
    important: 'महत्वपूर्ण',
    reviewDetails: 'अपने दस्तावेज़ स्थायी रूप से जमा करने से पहले अपने विवरण की समीक्षा करें',
    cannotChange: 'बैंक खाता एक बार जोड़ने के बाद बदला नहीं जा सकता',
    submitDetails: 'विवरण जमा करें',
    enterAccountNumber: 'अपना बैंक खाता संख्या दर्ज करें',
    confirmAccountNumber: 'अपना बैंक खाता संख्या की पुष्टि करें',
    enterIfscCode: '11-अंकीय बैंक IFSC कोड दर्ज करें',
    nameRequired: 'खाता धारक का नाम आवश्यक है',
    nameMinLength: 'नाम कम से कम 2 अक्षर का होना चाहिए',
    proofRequired: 'बैंक प्रमाण दस्तावेज़ आवश्यक है',
    sampleBankInfo: 'भारतीय स्टेट बैंक, मुंबई शाखा'
  }
}
