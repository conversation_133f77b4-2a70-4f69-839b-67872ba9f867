export default {
  common: {
    balance: 'Balance',
    deposit: 'Deposit',
    withdraw: 'Withdraw',
    transactions: 'Transactions',
    amount: 'Amount',
    submit: 'Submit',
    cancel: 'Cancel',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    copy: 'Copy',
    contact: 'Contact',
    today: 'Today',
    yesterday: 'Yesterday',
    total: 'Total',
    retry: 'Retry'
  },
  home: {
    title: 'My Balance',
    totalBalance: 'TOTAL BALANCE',
    amountAdded: 'AMOUNT ADDED(UNUTILISED)',
    winnings: 'WINNINGS',
    cashBonus: 'CASH BONUS',
    addCash: 'ADD CASH',
    withdrawInstantly: 'WITHDRAW INSTANTLY',
    verifyNow: 'VERIFY NOW',
    myTransactions: 'My Transactions',
    managePayments: 'Manage Payments',
    verifyAccount: 'verify your account to be eligible to withdraw',
    cashBonusRule: 'Maximum usable Cash Bonus per match=10% of Entry Fees',
    knowMore: 'Know more'
  },
  deposit: {
    title: 'Deposit',
    currentBalance: 'Current Balance',
    paymentMethod: 'Payment Method',
    paymentChannel: 'Payment Channel',
    depositNow: 'DEPOSIT NOW',
    minMaxAmount: 'Deposit min :₹{min} & max:₹{max} allowed each time',
    tips: 'Tips',
    cashBalance: 'Cash Balance',
    cashBonus: 'Cash Bonus',
    depositUsuallyCredited: 'Deposit is usually credited in minutes',
    depositSubmitted: 'Deposit {amount} submitted',
    depositSuccess: 'Deposit Success',
    depositFailed: 'Deposit failed, please try again',
    depositError: 'Deposit Failed',
    fee: 'Fee',
    bonus: 'Bonus',
    noAdditionalFees: 'No additional fees',
    optionCount: '{count, plural, =1 {option} other {options}}',
    noPaymentMethods: 'No payment methods available',
    includingFees: 'including fees'
  },

  // File Upload
  fileUpload: {
    clickToUpload: 'Click to upload or drag and drop',
    uploading: 'Uploading...',
    fileTypeNotSupported: 'File type not supported'
  },

  // Lazy Image
  lazyImage: {
    failedToLoad: 'Failed to load'
  },

  withdraw: {
    title: 'WITHDRAW',
    withdrawableBalance: 'Withdrawable Balance',
    addNewBankAccount: 'Add New Bank Account',
    withdrawNow: 'WITHDRAW NOW',
    minMaxAmount: 'Withdraw min :₹{min} & max:₹{max} allowed each time',
    addBankAccountDesc: 'Add a new bank account to withdraw to',
    withdrawUsuallyProcessed: 'Withdraw is usually processed in minutes'
  },

  transaction: {
    title: 'My Transactions',
    deposits: 'Deposits',
    withdrawals: 'Withdrawals',
    bet: 'Bet',
    bonus: 'Bonus',
    success: 'Success',
    inProcess: 'In-process',
    failed: 'Failed',
    refund: 'Refund',
    win: 'Win',
    loss: 'Loss',
    transferIn: 'Transfer in',
    transferOut: 'Transfer Out',
    noTransactions: "You've not done any transactions till now",
    needHelp: 'Need help with this order? Tap to contact us',
    allBettingRecords: 'All your betting records are displayed here',
    allBonusRecords: 'All your bonus records are displayed here',
    noDepositTransactions: 'No deposit transactions found',
    noWithdrawalTransactions: 'No withdrawal transactions found',
    orderCopied: 'Order ID copied to clipboard',
    copyFailed: 'Copy failed, please copy order ID manually'
  },
  payment: {
    title: 'Manage Payments',
    myBankAccounts: 'My Bank Accounts',
    myWallets: 'My Wallets UPI ID',
    verifyNewBankAccount: 'Verify New Bank Account',
    linkUpiWallets: 'Link your UPI wallets for quick payments',
    noUpiWallets: 'No UPI wallets linked',
    linkUpiWalletsFaster: 'Link your UPI wallets for faster payments',
    linked: 'Linked',
    notLinked: 'Not linked',
    link: 'Link',
    unlink: 'Unlink',
    upiId: 'UPI ID',
    upiPlaceholder: 'yourname@upi',
    paytm: 'Paytm',
    phonepe: 'Phonepe',
    gpay: 'Gpay',
    otherUpi: 'Other UPI ID',
    // Wallet descriptions
    paytmDescription: 'Digital wallet and UPI payments',
    phonepeDescription: 'UPI payments and money transfer',
    gpayDescription: 'Google Pay UPI payments',
    otherUpiDescription: 'Any UPI ID',
    upiPaymentMethod: 'UPI payment method',
    // Bank account description
    manageBankAccountsDesc: 'Manage your bank accounts for withdrawals',
    // Help section
    needHelpWithPayments: 'Need help with payments?',
    contactSupportForPayments: 'Contact our support team for assistance with payment methods',
    contactSupport: 'Contact Support'
  },
  bankAccount: {
    title: 'Add New Bank Account',
    accountNumber: 'Account Number',
    retypeAccountNumber: 'Retype Account Number',
    ifscCode: 'IFSC Code',
    name: 'Name',
    verified: 'Verified',
    pending: 'Pending',
    bankProof: 'BANK PROOF',
    important: 'IMPORTANT',
    reviewDetails: 'Review your details before submitting your documents permanently',
    cannotChange: "Bank account once added can't be changed later",
    submitDetails: 'SUBMIT DETAILS',
    enterAccountNumber: 'Enter your Bank Account Number',
    confirmAccountNumber: 'Confirm your Bank Account Number',
    enterIfscCode: 'Enter 11-digit Bank IFSC Code',
    nameRequired: 'Account holder name is required',
    nameMinLength: 'Name must be at least 2 characters',
    proofRequired: 'Bank proof document is required',
    sampleBankInfo: 'State Bank of India, Mumbai Branch'
  }
}
