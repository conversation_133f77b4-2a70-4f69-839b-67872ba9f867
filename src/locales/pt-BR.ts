export default {
  common: {
    balance: 'Sal<PERSON>',
    deposit: 'Dep<PERSON>ito',
    withdraw: 'Saque',
    transactions: 'Transações',
    amount: '<PERSON><PERSON>',
    submit: 'Enviar',
    cancel: 'Cancelar',
    loading: 'Carregando...',
    error: 'Erro',
    success: 'Sucesso',
    confirm: 'Confirmar',
    back: 'Voltar',
    next: 'Próximo',
    save: '<PERSON><PERSON>',
    delete: 'Excluir',
    edit: 'Editar',
    add: 'Adicionar',
    copy: '<PERSON>pia<PERSON>',
    contact: '<PERSON>tat<PERSON>',
    today: 'Hoje',
    yesterday: 'Ontem',
    total: 'Total',
    retry: 'Tentar novamente'
  },
  home: {
    title: 'Meu <PERSON>',
    totalBalance: 'SALDO TOTAL',
    amountAdded: 'VALOR ADICIONADO (NÃO UTILIZADO)',
    winnings: 'GANHOS',
    cashBonus: 'BÔNUS EM DINHEIRO',
    addCash: 'ADICIONAR DINHEIRO',
    withdrawInstantly: 'SACAR INSTANTANEAMENTE',
    verifyNow: 'VERIFICAR AGORA',
    myTransactions: 'Minhas Transações',
    managePayments: 'Gerenciar Pagamentos',
    verifyAccount: 'verifique sua conta para ser elegível para saque',
    cashBonusRule: 'Bônus em dinheiro máximo utilizável por partida = 10% das taxas de entrada',
    knowMore: 'Saiba mais'
  },
  deposit: {
    title: 'Depósito',
    currentBalance: 'Saldo Atual',
    paymentMethod: 'Método de Pagamento',
    paymentChannel: 'Canal de Pagamento',
    depositNow: 'DEPOSITAR AGORA',
    minMaxAmount: 'Depósito mín: ₹{min} e máx: ₹{max} permitido a cada vez',
    tips: 'Dicas',
    cashBalance: 'Saldo em Dinheiro',
    cashBonus: 'Bônus em Dinheiro',
    depositUsuallyCredited: 'O depósito geralmente é creditado em minutos',
    depositSubmitted: 'Depósito {amount} enviado',
    depositSuccess: 'Depósito Bem-sucedido',
    depositFailed: 'Depósito falhou, tente novamente',
    depositError: 'Falha no Depósito',
    fee: 'Taxa',
    bonus: 'Bônus',
    noAdditionalFees: 'Sem taxas adicionais',
    optionCount: '{count, plural, =1 {opção} other {opções}}',
    noPaymentMethods: 'Nenhum método de pagamento disponível',
    includingFees: 'incluindo taxas'
  },

  // Upload de Arquivo
  fileUpload: {
    clickToUpload: 'Clique para fazer upload ou arraste e solte',
    uploading: 'Enviando...',
    fileTypeNotSupported: 'Tipo de arquivo não suportado'
  },

  // Imagem Lazy
  lazyImage: {
    failedToLoad: 'Falha ao carregar'
  },

  withdraw: {
    title: 'SAQUE',
    withdrawableBalance: 'Saldo Disponível para Saque',
    addNewBankAccount: 'Adicionar Nova Conta Bancária',
    withdrawNow: 'SACAR AGORA',
    minMaxAmount: 'Saque mín: ₹{min} e máx: ₹{max} permitido a cada vez',
    addBankAccountDesc: 'Adicione uma nova conta bancária para sacar',
    withdrawUsuallyProcessed: 'O saque geralmente é processado em minutos'
  },

  transaction: {
    title: 'Minhas Transações',
    deposits: 'Depósitos',
    withdrawals: 'Saques',
    bet: 'Aposta',
    bonus: 'Bônus',
    success: 'Sucesso',
    inProcess: 'Em processamento',
    failed: 'Falhou',
    refund: 'Reembolso',
    win: 'Vitória',
    loss: 'Perda',
    transferIn: 'Transferência de entrada',
    transferOut: 'Transferência de saída',
    noTransactions: 'Você ainda não fez nenhuma transação',
    needHelp: 'Precisa de ajuda com este pedido? Toque para nos contatar',
    allBettingRecords: 'Todos os seus registros de apostas são exibidos aqui',
    allBonusRecords: 'Todos os seus registros de bônus são exibidos aqui',
    noDepositTransactions: 'Nenhuma transação de depósito encontrada',
    noWithdrawalTransactions: 'Nenhuma transação de saque encontrada',
    orderCopied: 'ID do pedido copiado para a área de transferência',
    copyFailed: 'Falha ao copiar, copie o ID do pedido manualmente'
  },
  payment: {
    title: 'Gerenciar Pagamentos',
    myBankAccounts: 'Minhas Contas Bancárias',
    myWallets: 'Minhas Carteiras UPI ID',
    verifyNewBankAccount: 'Verificar Nova Conta Bancária',
    linkUpiWallets: 'Vincule suas carteiras UPI para pagamentos rápidos',
    noUpiWallets: 'Nenhuma carteira UPI vinculada',
    linkUpiWalletsFaster: 'Vincule suas carteiras UPI para pagamentos mais rápidos',
    linked: 'Vinculado',
    notLinked: 'Não vinculado',
    link: 'Vincular',
    unlink: 'Desvincular',
    upiId: 'UPI ID',
    upiPlaceholder: 'seunome@upi',
    paytm: 'Paytm',
    phonepe: 'PhonePe',
    gpay: 'GPay',
    otherUpi: 'Outro UPI ID',
    // Descrições das carteiras
    paytmDescription: 'Carteira digital e pagamentos UPI',
    phonepeDescription: 'Pagamentos UPI e transferência de dinheiro',
    gpayDescription: 'Pagamentos UPI do Google Pay',
    otherUpiDescription: 'Qualquer UPI ID',
    upiPaymentMethod: 'Método de pagamento UPI',
    // Descrição da conta bancária
    manageBankAccountsDesc: 'Gerencie suas contas bancárias para saques',
    // Seção de ajuda
    needHelpWithPayments: 'Precisa de ajuda com pagamentos?',
    contactSupportForPayments:
      'Entre em contato com nossa equipe de suporte para assistência com métodos de pagamento',
    contactSupport: 'Contatar Suporte'
  },
  bankAccount: {
    title: 'Adicionar Nova Conta Bancária',
    accountNumber: 'Número da Conta',
    retypeAccountNumber: 'Digite Novamente o Número da Conta',
    ifscCode: 'Código IFSC',
    name: 'Nome',
    verified: 'Verificado',
    pending: 'Pendente',
    bankProof: 'COMPROVANTE BANCÁRIO',
    important: 'IMPORTANTE',
    reviewDetails: 'Revise seus detalhes antes de enviar seus documentos permanentemente',
    cannotChange: 'A conta bancária uma vez adicionada não pode ser alterada posteriormente',
    submitDetails: 'ENVIAR DETALHES',
    enterAccountNumber: 'Digite o número da sua conta bancária',
    confirmAccountNumber: 'Confirme o número da sua conta bancária',
    enterIfscCode: 'Digite o código IFSC bancário de 11 dígitos',
    nameRequired: 'Nome do titular da conta é obrigatório',
    nameMinLength: 'Nome deve ter pelo menos 2 caracteres',
    proofRequired: 'Documento de comprovante bancário é obrigatório',
    sampleBankInfo: 'State Bank of India, Filial Mumbai'
  }
}
