export default {
  common: {
    balance: '余额',
    deposit: '充值',
    withdraw: '提现',
    transactions: '交易记录',
    amount: '金额',
    submit: '提交',
    cancel: '取消',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    confirm: '确认',
    back: '返回',
    next: '下一步',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    copy: '复制',
    contact: '联系我们',
    today: '今天',
    yesterday: '昨天',
    total: '总计',
    retry: '重试'
  },
  home: {
    title: '我的余额',
    totalBalance: '总余额',
    amountAdded: '已添加金额(未使用)',
    winnings: '奖金',
    cashBonus: '现金奖金',
    addCash: '充值',
    withdrawInstantly: '立即提现',
    verifyNow: '立即验证',
    myTransactions: '我的交易',
    managePayments: '管理支付方式',
    verifyAccount: '验证您的账户以获得提现资格',
    cashBonusRule: '每场比赛最多可使用现金奖金=报名费的10%',
    knowMore: '了解更多'
  },
  deposit: {
    title: '充值',
    currentBalance: '当前余额',
    paymentMethod: '支付方式',
    paymentChannel: '支付渠道',
    depositNow: '立即充值',
    minMaxAmount: '充值最小金额：₹{min}，最大金额：₹{max}',
    tips: '提示',
    cashBalance: '现金余额',
    cashBonus: '现金奖金',
    depositUsuallyCredited: '充值通常在几分钟内到账',
    depositSubmitted: '充值 {amount} 已提交',
    depositSuccess: '充值成功',
    depositFailed: '充值失败，请重试',
    depositError: '充值失败',
    fee: '手续费',
    bonus: '奖金',
    noAdditionalFees: '无额外费用',
    optionCount: '{count, plural, =1 {个选项} other {个选项}}',
    noPaymentMethods: '暂无可用的支付方式',
    includingFees: '含手续费'
  },

  // 文件上传
  fileUpload: {
    clickToUpload: '点击上传或拖拽文件',
    uploading: '上传中...',
    fileTypeNotSupported: '不支持的文件类型'
  },

  // 懒加载图片
  lazyImage: {
    failedToLoad: '加载失败'
  },

  withdraw: {
    title: '提现',
    withdrawableBalance: '可提现余额',
    addNewBankAccount: '添加新银行账户',
    withdrawNow: '立即提现',
    minMaxAmount: '提现最小金额：₹{min}，最大金额：₹{max}',
    addBankAccountDesc: '添加新银行账户以进行提现',
    withdrawUsuallyProcessed: '提现通常在几分钟内处理完成'
  },

  transaction: {
    title: '我的交易',
    deposits: '存款',
    withdrawals: '提现',
    bet: '投注',
    bonus: '奖金',
    success: '成功',
    inProcess: '处理中',
    failed: '失败',
    refund: '退款',
    win: '赢',
    loss: '输',
    transferIn: '转入',
    transferOut: '转出',
    noTransactions: '您还没有进行任何交易',
    needHelp: '需要帮助处理此订单？点击联系我们',
    allBettingRecords: '所有投注记录都显示在这里',
    allBonusRecords: '所有奖金记录都显示在这里',
    noDepositTransactions: '未找到充值交易记录',
    noWithdrawalTransactions: '未找到提现交易记录',
    orderCopied: '订单号已复制到剪贴板',
    copyFailed: '复制失败，请手动复制订单号'
  },
  payment: {
    title: '管理支付方式',
    myBankAccounts: '我的银行账户',
    myWallets: '我的钱包 UPI ID',
    verifyNewBankAccount: '验证新银行账户',
    linkUpiWallets: '关联您的UPI钱包以便快速支付',
    noUpiWallets: '未关联UPI钱包',
    linkUpiWalletsFaster: '关联您的UPI钱包以便更快支付',
    linked: '已关联',
    notLinked: '未关联',
    link: '关联',
    unlink: '取消关联',
    upiId: 'UPI ID',
    upiPlaceholder: 'yourname@upi',
    paytm: 'Paytm',
    phonepe: 'PhonePe',
    gpay: 'GPay',
    otherUpi: '其他 UPI ID',
    // 钱包描述
    paytmDescription: '数字钱包和UPI支付',
    phonepeDescription: 'UPI支付和转账',
    gpayDescription: 'Google Pay UPI支付',
    otherUpiDescription: '任意UPI ID',
    upiPaymentMethod: 'UPI支付方式',
    // 银行账户描述
    manageBankAccountsDesc: '管理您的银行账户以进行提现',
    // 帮助部分
    needHelpWithPayments: '需要支付帮助？',
    contactSupportForPayments: '联系我们的支持团队获取支付方式帮助',
    contactSupport: '联系客服'
  },
  bankAccount: {
    title: '添加新银行账户',
    accountNumber: '账户号码',
    retypeAccountNumber: '重新输入账户号码',
    ifscCode: 'IFSC 代码',
    name: '姓名',
    verified: '已验证',
    pending: '待审核',
    bankProof: '银行证明',
    important: '重要提示',
    reviewDetails: '提交文件前请仔细检查您的详细信息',
    cannotChange: '银行账户一旦添加就无法更改',
    submitDetails: '提交详细信息',
    enterAccountNumber: '输入您的银行账户号码',
    confirmAccountNumber: '确认您的银行账户号码',
    enterIfscCode: '输入11位银行IFSC代码',
    nameRequired: '账户持有人姓名是必填项',
    nameMinLength: '姓名至少需要2个字符',
    proofRequired: '银行证明文件是必填项',
    sampleBankInfo: '印度国家银行，孟买分行'
  }
}
