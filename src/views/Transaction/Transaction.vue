<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader :title="$t('transaction.title')" :show-back-button="true" />

    <!-- Loading State -->
    <div v-if="transactionStore.loading" class="flex items-center justify-center py-8">
      <LoadingSpinner />
    </div>

    <!-- Error State -->
    <div v-else-if="transactionStore.error" class="p-4">
      <ErrorMessage :message="transactionStore.error" @retry="handleRetry" />
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Transaction Tabs -->
      <div class="bg-white border-b border-gray-200 sticky top-16 z-40">
        <div class="flex overflow-x-auto">
          <button
            v-for="tab in transactionTabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'flex-shrink-0 px-6 py-4 text-sm font-medium border-b-2 transition-colors',
              activeTab === tab.key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            ]"
          >
            {{ tab.label }}
            <span
              v-if="tab.count > 0"
              class="ml-2 px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full"
            >
              {{ tab.count }}
            </span>
          </button>
        </div>
      </div>

      <!-- Status Filter (for each tab) -->
      <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex space-x-2 overflow-x-auto">
          <button
            v-for="status in statusFilters"
            :key="status.key"
            @click="activeStatus = status.key"
            :class="[
              'flex-shrink-0 px-4 py-2 text-sm font-medium rounded-full transition-colors',
              activeStatus === status.key
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            ]"
          >
            {{ status.label }}
          </button>
        </div>
      </div>

      <!-- Transaction List -->
      <div class="p-4">
        <!-- Deposits Tab -->
        <div v-if="activeTab === 'deposits'">
          <TransactionSection
            v-if="filteredDeposits.length > 0"
            :transactions="filteredDeposits"
            :grouped-by-date="true"
            @copy-order="handleCopyOrder"
          />
          <EmptyState
            v-else
            :title="$t('transaction.noTransactions')"
            :description="getEmptyStateDescription('deposits')"
            icon="credit-card"
          />
        </div>

        <!-- Withdrawals Tab -->
        <div v-if="activeTab === 'withdrawals'">
          <TransactionSection
            v-if="filteredWithdrawals.length > 0"
            :transactions="filteredWithdrawals"
            @copy-order="handleCopyOrder"
          />
          <EmptyState
            v-else
            :title="$t('transaction.noTransactions')"
            :description="getEmptyStateDescription('withdrawals')"
            icon="download"
          />
        </div>

        <!-- Bets Tab -->
        <div v-if="activeTab === 'bets'">
          <TransactionSection
            v-if="filteredBets.length > 0"
            :transactions="filteredBets"
            @copy-order="handleCopyOrder"
          />
          <EmptyState
            v-else
            :title="$t('transaction.noTransactions')"
            :description="$t('transaction.allBettingRecords')"
            icon="dice-6"
          />
        </div>

        <!-- Bonuses Tab -->
        <div v-if="activeTab === 'bonuses'">
          <TransactionSection
            v-if="filteredBonuses.length > 0"
            :transactions="filteredBonuses"
            @copy-order="handleCopyOrder"
          />
          <EmptyState
            v-else
            :title="$t('transaction.noTransactions')"
            :description="$t('transaction.allBonusRecords')"
            icon="gift"
          />
        </div>
      </div>

      <!-- Help Section -->
      <div v-if="transactionStore.transactionHistory?.url" class="p-4">
        <div class="bg-blue-50 rounded-xl p-4">
          <div class="flex items-center">
            <HelpCircle class="w-5 h-5 text-blue-600 mr-3" />
            <div class="flex-1">
              <p class="text-sm text-blue-900">
                {{ $t('transaction.needHelp') }}
              </p>
            </div>
            <ActionButton
              :text="$t('common.contact')"
              variant="outline"
              size="sm"
              @click="openSupport"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTransactionStore } from '@/stores/transaction'
import type { TransactionItem } from '@/types/api'
import { HelpCircle } from 'lucide-vue-next'

// Components
import AppHeader from '@/components/layout/AppHeader.vue'
import TransactionSection from '@/components/transaction/TransactionSection.vue'
import EmptyState from '@/components/common/EmptyState.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import ErrorMessage from '@/components/common/ErrorMessage.vue'

const { t } = useI18n()
const transactionStore = useTransactionStore()

// State
const activeTab = ref('deposits')
const activeStatus = ref('success') // Default to first filter option

// Watch for tab changes and update status filter
watch(activeTab, (newTab) => {
  // Set default status based on the new tab
  switch (newTab) {
    case 'deposits':
    case 'withdrawals':
      activeStatus.value = 'success'
      break
    case 'bets':
      activeStatus.value = 'win'
      break
    case 'bonuses':
      activeStatus.value = 'transferIn'
      break
    default:
      activeStatus.value = 'success'
  }
})

// Computed properties
const transactionTabs = computed(() => [
  {
    key: 'deposits',
    label: t('transaction.deposits'),
    count: transactionStore.deposits.length
  },
  {
    key: 'withdrawals',
    label: t('transaction.withdrawals'),
    count: transactionStore.withdrawals.length
  },
  {
    key: 'bets',
    label: t('transaction.bet'),
    count: transactionStore.bets.length
  },
  {
    key: 'bonuses',
    label: t('transaction.bonus'),
    count: transactionStore.bonuses.length
  }
])

// Dynamic status filters based on active tab (matching original website)
const statusFilters = computed(() => {
  switch (activeTab.value) {
    case 'deposits':
      return [
        { key: 'success', label: t('transaction.success') },
        { key: 'pending', label: t('transaction.inProcess') },
        { key: 'failed', label: t('transaction.failed') }
      ]
    case 'withdrawals':
      return [
        { key: 'success', label: t('transaction.success') },
        { key: 'pending', label: t('transaction.inProcess') },
        { key: 'refund', label: t('transaction.refund') },
        { key: 'failed', label: t('transaction.failed') }
      ]
    case 'bets':
      return [
        { key: 'win', label: t('transaction.win') },
        { key: 'loss', label: t('transaction.loss') }
      ]
    case 'bonuses':
      return [
        { key: 'transferIn', label: t('transaction.transferIn') },
        { key: 'transferOut', label: t('transaction.transferOut') }
      ]
    default:
      return []
  }
})

const filteredDeposits = computed(() => {
  return filterTransactionsByStatus(transactionStore.deposits)
})

const filteredWithdrawals = computed(() => {
  return filterTransactionsByStatus(transactionStore.withdrawals)
})

const filteredBets = computed(() => {
  return filterTransactionsByStatus(transactionStore.bets)
})

const filteredBonuses = computed(() => {
  return filterTransactionsByStatus(transactionStore.bonuses)
})

// Methods
const filterTransactionsByStatus = (transactions: TransactionItem[]) => {
  if (!activeStatus.value) {
    return transactions
  }

  // Different filtering logic based on tab type
  switch (activeTab.value) {
    case 'deposits':
    case 'withdrawals':
      const statusMap: Record<string, number> = {
        pending: 0,
        success: 1,
        failed: 2,
        refund: 3
      }
      const targetStatus = statusMap[activeStatus.value]
      return transactions.filter((transaction) => transaction.status === targetStatus)

    case 'bets':
      // For bets: win/loss filtering
      if (activeStatus.value === 'win') {
        return transactions.filter((transaction) => transaction.status === 1) // Assuming 1 = win
      } else if (activeStatus.value === 'loss') {
        return transactions.filter((transaction) => transaction.status === 2) // Assuming 2 = loss
      }
      return transactions

    case 'bonuses':
      // For bonuses: transfer in/out filtering based on title or memo
      if (activeStatus.value === 'transferIn') {
        return transactions.filter(
          (transaction) =>
            transaction.title?.toLowerCase().includes('transfer in') ||
            transaction.memo?.toLowerCase().includes('transfer in')
        )
      } else if (activeStatus.value === 'transferOut') {
        return transactions.filter(
          (transaction) =>
            transaction.title?.toLowerCase().includes('transfer out') ||
            transaction.memo?.toLowerCase().includes('transfer out')
        )
      }
      return transactions

    default:
      return transactions
  }
}

const getEmptyStateDescription = (type: string) => {
  switch (type) {
    case 'deposits':
      return t('transaction.noDepositTransactions')
    case 'withdrawals':
      return t('transaction.noWithdrawalTransactions')
    case 'bets':
      return t('transaction.allBettingRecords')
    case 'bonuses':
      return t('transaction.allBonusRecords')
    default:
      return t('transaction.noTransactions')
  }
}

const handleCopyOrder = async (orderId: string) => {
  const success = await transactionStore.copyOrderId(orderId)
  const { useToast } = await import('@/composables/useToast')
  const { success: showSuccess, error: showError } = useToast()

  if (success) {
    showSuccess(t('transaction.orderCopied'), t('common.success'))
  } else {
    showError(t('transaction.copyFailed'), t('common.error'))
  }
}

const handleRetry = () => {
  transactionStore.clearError()
  loadData()
}

const openSupport = () => {
  const supportUrl = transactionStore.transactionHistory?.url
  if (supportUrl) {
    window.open(supportUrl, '_blank')
  }
}

const loadData = async () => {
  await transactionStore.fetchTransactions()
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
