<template>
  <div class="min-h-screen bg-gray-50 p-4">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold text-gray-900 mb-6">API Integration Test</h1>
      
      <!-- Test Results -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- User Store Test -->
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h2 class="text-lg font-semibold mb-4">User Store Test</h2>
          <div class="space-y-2">
            <button
              @click="testUserBalance"
              :disabled="userStore.loading"
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {{ userStore.loading ? 'Loading...' : 'Test User Balance' }}
            </button>
            <div v-if="userStore.userInfo" class="text-sm text-gray-600">
              <p>Total Balance: {{ formatCurrency(userStore.totalBalance) }}</p>
              <p>Withdrawable: {{ formatCurrency(userStore.withdrawableBalance) }}</p>
              <p>KYC Status: {{ userStore.isKycVerified ? 'Verified' : 'Not Verified' }}</p>
            </div>
            <div v-if="userStore.error" class="text-sm text-red-600">
              Error: {{ userStore.error }}
            </div>
          </div>
        </div>
        
        <!-- Payment Store Test -->
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h2 class="text-lg font-semibold mb-4">Payment Store Test</h2>
          <div class="space-y-2">
            <button
              @click="testPaymentMethods"
              :disabled="paymentStore.loading"
              class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {{ paymentStore.loading ? 'Loading...' : 'Test Payment Methods' }}
            </button>
            <div v-if="paymentStore.paymentChannels.length > 0" class="text-sm text-gray-600">
              <p>Payment Channels: {{ paymentStore.paymentChannels.length }}</p>
              <p>Bank Accounts: {{ paymentStore.bankAccounts.length }}</p>
              <p>UPI Wallets: {{ Object.keys(paymentStore.upiWallets).length }}</p>
            </div>
            <div v-if="paymentStore.error" class="text-sm text-red-600">
              Error: {{ paymentStore.error }}
            </div>
          </div>
        </div>
        
        <!-- Transaction Store Test -->
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h2 class="text-lg font-semibold mb-4">Transaction Store Test</h2>
          <div class="space-y-2">
            <button
              @click="testTransactions"
              :disabled="transactionStore.loading"
              class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              {{ transactionStore.loading ? 'Loading...' : 'Test Transactions' }}
            </button>
            <div v-if="transactionStore.transactionHistory" class="text-sm text-gray-600">
              <p>Deposits: {{ transactionStore.deposits.length }}</p>
              <p>Withdrawals: {{ transactionStore.withdrawals.length }}</p>
              <p>Bets: {{ transactionStore.bets.length }}</p>
              <p>Bonuses: {{ transactionStore.bonuses.length }}</p>
            </div>
            <div v-if="transactionStore.error" class="text-sm text-red-600">
              Error: {{ transactionStore.error }}
            </div>
          </div>
        </div>
        
        <!-- Draw Info Test -->
        <div class="bg-white rounded-lg p-6 shadow-sm">
          <h2 class="text-lg font-semibold mb-4">Draw Info Test</h2>
          <div class="space-y-2">
            <button
              @click="testDrawInfo"
              :disabled="transactionStore.loading"
              class="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 disabled:opacity-50"
            >
              {{ transactionStore.loading ? 'Loading...' : 'Test Draw Info' }}
            </button>
            <div v-if="transactionStore.drawInfo" class="text-sm text-gray-600">
              <p>Withdrawable: {{ formatCurrency(transactionStore.withdrawableAmount) }}</p>
              <p>Min Withdraw: {{ formatCurrency(transactionStore.withdrawLimits.min) }}</p>
              <p>Max Withdraw: {{ formatCurrency(transactionStore.withdrawLimits.max) }}</p>
            </div>
            <div v-if="transactionStore.error" class="text-sm text-red-600">
              Error: {{ transactionStore.error }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Navigation Test -->
      <div class="mt-8 bg-white rounded-lg p-6 shadow-sm">
        <h2 class="text-lg font-semibold mb-4">Navigation Test</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
          <router-link
            to="/"
            class="bg-blue-100 text-blue-700 py-2 px-4 rounded-lg text-center hover:bg-blue-200 transition-colors"
          >
            Home
          </router-link>
          <router-link
            to="/pages/deposit/add/add"
            class="bg-green-100 text-green-700 py-2 px-4 rounded-lg text-center hover:bg-green-200 transition-colors"
          >
            Deposit
          </router-link>
          <router-link
            to="/pages/withdraw/index"
            class="bg-orange-100 text-orange-700 py-2 px-4 rounded-lg text-center hover:bg-orange-200 transition-colors"
          >
            Withdraw
          </router-link>
          <router-link
            to="/pages/transaction/transaction"
            class="bg-purple-100 text-purple-700 py-2 px-4 rounded-lg text-center hover:bg-purple-200 transition-colors"
          >
            Transactions
          </router-link>
          <router-link
            to="/pages/deposit/managepayment/managepayment"
            class="bg-indigo-100 text-indigo-700 py-2 px-4 rounded-lg text-center hover:bg-indigo-200 transition-colors"
          >
            Manage Payments
          </router-link>
          <router-link
            to="/pages/deposit/addcreditcard/addcreditcard"
            class="bg-pink-100 text-pink-700 py-2 px-4 rounded-lg text-center hover:bg-pink-200 transition-colors"
          >
            Add Bank Account
          </router-link>
        </div>
      </div>
      
      <!-- Language Test -->
      <div class="mt-8 bg-white rounded-lg p-6 shadow-sm">
        <h2 class="text-lg font-semibold mb-4">Language Test</h2>
        <LanguageSwitcher />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { usePaymentStore } from '@/stores/payment'
import { useTransactionStore } from '@/stores/transaction'
import { formatCurrency } from '@/utils/currency'
import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'

const userStore = useUserStore()
const paymentStore = usePaymentStore()
const transactionStore = useTransactionStore()

// Test methods
const testUserBalance = async () => {
  await userStore.fetchBalance()
}

const testPaymentMethods = async () => {
  await paymentStore.fetchPaymentMethods()
}

const testTransactions = async () => {
  await transactionStore.fetchTransactions()
}

const testDrawInfo = async () => {
  await transactionStore.fetchDrawInfo()
}
</script>
