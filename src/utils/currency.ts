// utils/currency.ts - 货币格式化工具函数

/**
 * 格式化货币显示
 * @param amount 金额
 * @param currency 货币符号，默认为印度卢比
 * @param locale 本地化设置，默认为印度
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(amount: number, currency = '₹', locale = 'en-IN'): string {
  return `${currency}${amount.toLocaleString(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })}`
}

/**
 * 解析货币字符串为数字
 * @param value 货币字符串
 * @returns 解析后的数字
 */
export function parseCurrency(value: string): number {
  return parseFloat(value.replace(/[^\d.-]/g, '')) || 0
}

/**
 * 格式化折扣显示
 * @param disrate 折扣率字符串
 * @param discoin 折扣金额
 * @returns 格式化后的折扣显示
 */
export function formatDiscount(disrate: string, discoin: number): string {
  if (disrate.includes('%')) {
    return disrate
  }
  return discoin > 0 ? `+${formatCurrency(discoin)}` : disrate
}
