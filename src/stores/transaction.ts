// stores/transaction.ts - 交易状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  TransactionHistory, 
  TransactionItem,
  DrawInfo 
} from '@/types/api'
import { 
  getTransactionHistory, 
  getDrawInfo,
  createDeposit,
  createWithdraw 
} from '@/services/api'

export const useTransactionStore = defineStore('transaction', () => {
  // 状态
  const transactionHistory = ref<TransactionHistory | null>(null)
  const drawInfo = ref<DrawInfo | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const deposits = computed(() => {
    if (!transactionHistory.value?.deplist) return []
    
    const allDeposits: TransactionItem[] = []
    Object.values(transactionHistory.value.deplist).forEach(dayDeposits => {
      allDeposits.push(...dayDeposits)
    })
    return allDeposits
  })

  const withdrawals = computed(() => {
    return transactionHistory.value?.drawlist || []
  })

  const bets = computed(() => {
    return transactionHistory.value?.betlist || []
  })

  const bonuses = computed(() => {
    return transactionHistory.value?.bonuslist || []
  })

  const hasTransactions = computed(() => {
    return deposits.value.length > 0 || 
           withdrawals.value.length > 0 || 
           bets.value.length > 0 || 
           bonuses.value.length > 0
  })

  const withdrawableAmount = computed(() => {
    return drawInfo.value?.dcoin || 0
  })

  const withdrawLimits = computed(() => {
    if (!drawInfo.value) return { min: 0, max: 0 }
    return {
      min: drawInfo.value.mincoin,
      max: drawInfo.value.maxcoin
    }
  })

  // 按日期分组的存款记录
  const depositsByDate = computed(() => {
    return transactionHistory.value?.deplist || {}
  })

  // 动作
  const fetchTransactions = async (page: number = 1, size: number = 10) => {
    try {
      loading.value = true
      error.value = null
      
      const data = await getTransactionHistory(page, size)
      transactionHistory.value = data
      currentPage.value = page
      pageSize.value = size
      
      console.log('Transactions fetched successfully:', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch transactions'
      console.error('Failed to fetch transactions:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchDrawInfo = async () => {
    try {
      loading.value = true
      error.value = null
      
      const data = await getDrawInfo()
      drawInfo.value = data
      
      console.log('Draw info fetched successfully:', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch draw info'
      console.error('Failed to fetch draw info:', err)
    } finally {
      loading.value = false
    }
  }

  const makeDeposit = async (depositData: {
    amount: number
    channelId: number
    pageId: number
  }) => {
    try {
      loading.value = true
      error.value = null
      
      const result = await createDeposit(depositData)
      
      // 重新获取交易历史
      await fetchTransactions(currentPage.value, pageSize.value)
      
      console.log('Deposit created successfully:', result)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create deposit'
      console.error('Failed to create deposit:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const makeWithdraw = async (withdrawData: {
    amount: number
    bankId: number
  }) => {
    try {
      loading.value = true
      error.value = null
      
      const result = await createWithdraw(withdrawData)
      
      // 重新获取交易历史和提现信息
      await Promise.all([
        fetchTransactions(currentPage.value, pageSize.value),
        fetchDrawInfo()
      ])
      
      console.log('Withdraw created successfully:', result)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create withdraw'
      console.error('Failed to create withdraw:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // 格式化交易状态
  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'text-yellow-600' // 处理中
      case 1: return 'text-green-600'  // 成功
      case 2: return 'text-red-600'    // 失败
      case 3: return 'text-blue-600'   // 退款
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: number) => {
    switch (status) {
      case 0: return 'clock'      // 处理中
      case 1: return 'check'      // 成功
      case 2: return 'x'          // 失败
      case 3: return 'refresh-cw' // 退款
      default: return 'help-circle'
    }
  }

  // 格式化金额显示
  const formatAmount = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    return `₹${numAmount.toLocaleString('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`
  }

  // 复制订单号
  const copyOrderId = async (orderId: string) => {
    try {
      await navigator.clipboard.writeText(orderId)
      console.log('Order ID copied:', orderId)
      return true
    } catch (err) {
      console.error('Failed to copy order ID:', err)
      return false
    }
  }

  return {
    // 状态
    transactionHistory,
    drawInfo,
    loading,
    error,
    currentPage,
    pageSize,
    
    // 计算属性
    deposits,
    withdrawals,
    bets,
    bonuses,
    hasTransactions,
    withdrawableAmount,
    withdrawLimits,
    depositsByDate,
    
    // 动作
    fetchTransactions,
    fetchDrawInfo,
    makeDeposit,
    makeWithdraw,
    clearError,
    getStatusColor,
    getStatusIcon,
    formatAmount,
    copyOrderId,
  }
})
