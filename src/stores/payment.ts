// stores/payment.ts - 支付状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { PaymentChannel, BankAccount, UpiWallet, PaymentPage } from '@/types/api'
import { getBanksAndWallets, addBankAccount, linkUpiWallet } from '@/services/api'
import { formatDiscount } from '@/utils/currency'

export const usePaymentStore = defineStore('payment', () => {
  // 状态
  const paymentChannels = ref<PaymentChannel[]>([])
  const bankAccounts = ref<BankAccount[]>([])
  const upiWallets = ref<Record<string, UpiWallet>>({})
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const availablePaymentMethods = computed(() => {
    return paymentChannels.value.filter((channel) => channel.pages.length > 0)
  })

  const verifiedBankAccounts = computed(() => {
    return bankAccounts.value.filter((account) => account.isVerified)
  })

  const linkedUpiWallets = computed(() => {
    return Object.values(upiWallets.value).filter((wallet) => wallet.checked)
  })

  const hasPaymentMethods = computed(() => {
    return verifiedBankAccounts.value.length > 0 || linkedUpiWallets.value.length > 0
  })

  // 获取支付方式的最小/最大金额
  const getPaymentLimits = (channelId: number) => {
    const channel = paymentChannels.value.find((c) => c.id === channelId)
    if (!channel) return { min: 0, max: 0 }

    return {
      min: channel.mincoin,
      max: channel.maxcoin
    }
  }

  // 获取支付页面信息
  const getPaymentPages = (channelId: number): PaymentPage[] => {
    const channel = paymentChannels.value.find((c) => c.id === channelId)
    return channel?.pages || []
  }

  // 动作
  const fetchPaymentMethods = async () => {
    try {
      loading.value = true
      error.value = null

      const data = await getBanksAndWallets()
      bankAccounts.value = data.banks
      upiWallets.value = data.upis

      console.log('Payment methods fetched successfully:', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch payment methods'
      console.error('Failed to fetch payment methods:', err)
    } finally {
      loading.value = false
    }
  }

  const setPaymentChannels = (channels: PaymentChannel[]) => {
    paymentChannels.value = channels
  }

  const addNewBankAccount = async (accountData: {
    accountNumber: string
    ifscCode: string
    accountHolderName: string
    bankName: string
  }) => {
    try {
      loading.value = true
      error.value = null

      const result = await addBankAccount(accountData)

      // 重新获取银行账户列表
      await fetchPaymentMethods()

      console.log('Bank account added successfully:', result)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to add bank account'
      console.error('Failed to add bank account:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const linkWallet = async (walletData: { type: number; identifier: string }) => {
    try {
      loading.value = true
      error.value = null

      const result = await linkUpiWallet(walletData)

      // 重新获取钱包列表
      await fetchPaymentMethods()

      console.log('UPI wallet linked successfully:', result)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to link UPI wallet'
      console.error('Failed to link UPI wallet:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // 计算实际支付金额
  const calculatePaymentAmount = (amount: number, channelId: number, pageId?: number) => {
    const channel = paymentChannels.value.find((c) => c.id === channelId)
    if (!channel) return amount

    let page = channel.pages[0] // 默认使用第一个页面
    if (pageId) {
      const foundPage = channel.pages.find((p) => p.id === pageId)
      if (foundPage) page = foundPage
    }

    if (!page) return amount

    // 计算费用
    const fee = page.rate > 0 ? amount * page.rate : page.discoin
    return amount + fee
  }

  return {
    // 状态
    paymentChannels,
    bankAccounts,
    upiWallets,
    loading,
    error,

    // 计算属性
    availablePaymentMethods,
    verifiedBankAccounts,
    linkedUpiWallets,
    hasPaymentMethods,

    // 动作
    fetchPaymentMethods,
    setPaymentChannels,
    addNewBankAccount,
    linkWallet,
    clearError,
    getPaymentLimits,
    getPaymentPages,
    formatDiscount,
    calculatePaymentAmount
  }
})
