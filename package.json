{"name": "paysms-app", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:analyze": "vite build --mode analyze", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "serve": "vite preview --port 4173", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@vueuse/core": "^13.6.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.536.0", "pinia": "^3.0.3", "radix-vue": "^1.9.17", "tailwind-merge": "^3.3.1", "vue": "^3.5.18", "vue-i18n": "9", "vue-router": "4"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "terser": "^5.43.1", "typescript": "^5.9.2", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.5"}}